const ErrorResponse = require("../../utils/errorResponse");
const Setting = require("../../models/Setting");
const { validationResult } = require("express-validator");
const fs = require("fs");
const path = require("path");

// @desc    Get platform settings
// @route   GET /api/admin/settings
// @access  Private/Admin
exports.getAdminSettings = async (req, res, next) => {
  try {
    const settings = await Setting.getSingleton();

    res.status(200).json({
      success: true,
      data: settings,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update platform settings
// @route   PUT /api/admin/settings
// @access  Private/Admin
exports.updateAdminSettings = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        success: false, 
        errors: errors.array() 
      });
    }

    // Get the singleton settings document
    let settings = await Setting.getSingleton();

    // Update fields that are provided in the request
    if (req.body.general) {
      Object.keys(req.body.general).forEach(key => {
        if (req.body.general[key] !== undefined) {
          settings.general[key] = req.body.general[key];
        }
      });
    }

    if (req.body.financial) {
      Object.keys(req.body.financial).forEach(key => {
        if (req.body.financial[key] !== undefined) {
          settings.financial[key] = req.body.financial[key];
        }
      });
    }

    // Set updatedBy field
    settings.updatedBy = req.user.id;

    // Save the updated settings
    await settings.save();

    res.status(200).json({
      success: true,
      data: settings,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Upload site logo
// @route   POST /api/admin/settings/upload-logo
// @access  Private/Admin
exports.uploadSiteLogo = async (req, res, next) => {
  try {
    if (!req.file) {
      return next(new ErrorResponse("Please upload a logo file", 400));
    }

    // Create uploads/logos directory if it doesn't exist
    const logoDir = path.join(__dirname, "../../uploads/logos");
    if (!fs.existsSync(logoDir)) {
      fs.mkdirSync(logoDir, { recursive: true });
    }

    // Get the file path relative to the uploads directory
    const logoPath = `/uploads/logos/${req.file.filename}`;

    // Get the singleton settings document
    let settings = await Setting.getSingleton();

    // Delete old logo file if it exists
    if (settings.general.siteLogo && settings.general.siteLogo.startsWith('/uploads/')) {
      const oldLogoPath = path.join(__dirname, "../..", settings.general.siteLogo);
      if (fs.existsSync(oldLogoPath)) {
        fs.unlinkSync(oldLogoPath);
      }
    }

    // Update the site logo path
    settings.general.siteLogo = logoPath;
    settings.updatedBy = req.user.id;

    // Save the updated settings
    await settings.save();

    res.status(200).json({
      success: true,
      data: {
        siteLogo: logoPath,
        message: "Logo uploaded successfully",
      },
    });
  } catch (err) {
    // Clean up uploaded file if there's an error
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    next(err);
  }
};
