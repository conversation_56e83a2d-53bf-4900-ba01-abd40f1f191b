const express = require('express');
const { check } = require('express-validator');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const {
  getAdminSettings,
  updateAdminSettings,
  uploadSiteLogo
} = require('../../controllers/admin/settings');

const { protect, authorize } = require('../../middleware/auth');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const logoDir = 'uploads/logos/';
    // Create directory if it doesn't exist
    if (!fs.existsSync(logoDir)) {
      fs.mkdirSync(logoDir, { recursive: true });
    }
    cb(null, logoDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'logo-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: function (req, file, cb) {
    // Check file type
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed!'), false);
    }
  }
});

const router = express.Router();

// All admin settings routes are protected and admin-only
router.use(protect);
router.use(authorize('admin'));

// Get platform settings
router.get('/', getAdminSettings);

// Upload site logo
router.post('/upload-logo', upload.single('logo'), uploadSiteLogo);

// Update platform settings
router.put('/', [
  // General settings validation
  check('general.siteName', 'Site name must be a string').optional().isString(),
  check('general.siteLogo', 'Site logo must be a string').optional().isString(),
  check('general.contactEmail', 'Contact email must be valid').optional().isEmail(),
  check('general.contactPhone', 'Contact phone must be a string').optional().isString(),
  check('general.address', 'Address must be a string').optional().isString(),
  check('general.supportLink', 'Support link must be a valid URL').optional().isURL(),
  check('general.maintenanceMode', 'Maintenance mode must be boolean').optional().isBoolean(),

  // Financial settings validation
  check('financial.platformCommissionPercentage', 'Platform commission must be a number between 0 and 100')
    .optional()
    .isFloat({ min: 0, max: 100 })
], updateAdminSettings);

module.exports = router;
