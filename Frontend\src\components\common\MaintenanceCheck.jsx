import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import MaintenanceMode from '../../pages/MaintenanceMode';

const MaintenanceCheck = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [isMaintenanceMode, setIsMaintenanceMode] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  
  // Get user from auth state
  const user = useSelector((state) => state.auth?.user);
  const isAdmin = user?.role === 'admin';

  // Routes that should be accessible even during maintenance
  const allowedRoutes = [
    '/maintenance',
    '/admin',
    '/auth',
    '/signup',
    '/otp-verification'
  ];

  // Check if current route is allowed during maintenance
  const isRouteAllowed = allowedRoutes.some(route => 
    location.pathname.startsWith(route)
  );

  useEffect(() => {
    const checkMaintenanceMode = async () => {
      try {
        // Fetch public settings to check maintenance mode
        const response = await fetch('/api/settings/public');
        if (response.ok) {
          const data = await response.json();
          const maintenanceMode = data.data?.maintenanceMode || false;
          setIsMaintenanceMode(maintenanceMode);
        }
      } catch (error) {
        console.error('Error checking maintenance mode:', error);
        // If we can't check, assume no maintenance mode
        setIsMaintenanceMode(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkMaintenanceMode();
  }, []);

  // Show loading while checking maintenance mode
  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }}>
        <div style={{
          width: '40px',
          height: '40px',
          border: '3px solid rgba(255,255,255,0.3)',
          borderTop: '3px solid white',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
      </div>
    );
  }

  // If maintenance mode is enabled and user is not admin and route is not allowed
  if (isMaintenanceMode && !isAdmin && !isRouteAllowed) {
    return <MaintenanceMode />;
  }

  // If we're on maintenance route but maintenance is not enabled, redirect to home
  if (location.pathname === '/maintenance' && !isMaintenanceMode) {
    navigate('/', { replace: true });
    return null;
  }

  // Normal rendering
  return children;
};

export default MaintenanceCheck;
